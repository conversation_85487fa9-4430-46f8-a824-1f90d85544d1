# RTL Course - Local Development Guide

This guide explains how to run the RTL Course application locally on your machine.

## Quick Start

### Option 1: Run Both Frontend and Backend Together
```bash
./start_local_dev.sh
```

This script will:
- Set up Python virtual environment for backend
- Install all dependencies
- Start backend server on http://localhost:8000
- Start frontend server on http://localhost:3000
- Both servers will run simultaneously

### Option 2: Run Backend Only
```bash
./run_backend.sh
```

This will start just the backend server on http://localhost:8000

### Option 3: Manual Setup

#### Backend Setup
```bash
cd backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
python3 run_server.py
```

#### Frontend Setup (in a separate terminal)
```bash
cd frontend
yarn install
yarn start
```

## What's Running

- **Backend Server**: http://localhost:8000
  - API endpoints available at `/api/*`
  - Interactive API documentation at http://localhost:8000/docs
  - Uses mock database (no MongoDB required)

- **Frontend Server**: http://localhost:3000
  - React application with hot reload
  - Configured to connect to local backend

## Key Features

### Mock Database
The local development setup uses a mock database instead of MongoDB, which includes:
- 3 sample lessons with exercises
- User progress tracking
- Test result storage
- No external database dependencies

### API Endpoints
- `GET /api/health` - Health check
- `GET /api/lessons` - Get all lessons
- `GET /api/lessons/{id}` - Get specific lesson
- `GET /api/progress/{user_id}` - Get user progress
- `POST /api/progress/{user_id}` - Update user progress
- `POST /api/test/run` - Run RTL tests

### Test Runner
The backend includes a fully functional React Testing Library test runner that:
- Executes real RTL tests in a Node.js environment
- Provides detailed feedback and error messages
- Supports Jest testing framework
- Returns coverage metrics and performance data

## Troubleshooting

### "No response from server" Error
If you see this error, make sure:
1. Backend server is running on http://localhost:8000
2. Frontend .env file points to `REACT_APP_BACKEND_URL=http://localhost:8000`
3. No firewall is blocking the connection

### Port Already in Use
If port 8000 or 3000 is already in use:
- Backend: Edit `backend/run_server.py` and change the port
- Frontend: The React dev server will automatically suggest an alternative port

### Dependencies Issues
- Backend: Make sure you're in the virtual environment (`source venv/bin/activate`)
- Frontend: Try deleting `node_modules` and running `yarn install` again

## Development Workflow

1. Start both servers using `./start_local_dev.sh`
2. Open http://localhost:3000 in your browser
3. Make changes to the code
4. Backend auto-reloads on Python file changes
5. Frontend auto-reloads on React file changes
6. Test your changes in the browser

## Testing

### Backend Tests
```bash
cd backend
source venv/bin/activate
python -m pytest test_backend_integration.py -v
```

### Frontend Tests
```bash
cd frontend
yarn test
```

## Production vs Development

- **Development**: Uses mock database, runs on localhost
- **Production**: Would use real MongoDB, deployed to cloud services

The local development setup is designed to be completely self-contained and doesn't require any external services.

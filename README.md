# React Testing Library Best Practices Course

An interactive web application for learning React Testing Library best practices through hands-on coding exercises and real-world examples.

## 🎯 Course Objectives

Master the 7 core principles of React Testing Library:

1. **Accessibility-First Queries** - Use queries that mirror user interactions
2. **User-Centric Testing** - Write tests from user perspective
3. **Query Priority Hierarchy** - Follow recommended query order
4. **Simple and Maintainable Tests** - Keep tests focused and clear
5. **Real Usage Patterns** - Test components in realistic contexts
6. **Minimal Mocking** - Mock only external dependencies
7. **Async Testing Done Right** - Master waitFor and findBy patterns

## 🚀 Features

- **Interactive Code Editor** - Monaco Editor with syntax highlighting
- **Real Test Execution** - Run actual tests in the browser (coming soon)
- **Progressive Learning** - Lessons unlock as you complete them
- **Immediate Feedback** - See test results and coverage instantly
- **Progress Tracking** - Track completion and mastery
- **Accessible Design** - AA compliant interface
- **Responsive Layout** - Works on all screen sizes

## 📚 Course Structure

Each lesson includes:
- **Learning Objectives** - Clear goals for each session
- **Key Concepts** - Core principles explained
- **Examples** - Good vs bad code patterns
- **Interactive Practice** - Hands-on coding exercises
- **Mock Test Results** - Immediate feedback with coverage (real execution coming in backend integration)

## 🛠 Technical Stack

- **Frontend**: React 19 + JSX
- **Styling**: Tailwind CSS + Custom Components
- **Code Editor**: Monaco Editor
- **Testing**: Mock test execution (real RTL integration coming soon)
- **State Management**: React Hooks + Local Storage
- **Routing**: React Router DOM
- **Backend**: FastAPI + MongoDB (for real test execution)

## 📦 Installation & Setup

```bash
# Install frontend dependencies
cd frontend
yarn install

# Start development server
yarn start

# Backend setup (for future real test execution)
cd backend
pip install -r requirements.txt
python -m uvicorn server:app --reload --host 0.0.0.0 --port 8001
```

## 🎨 Design Features

- **Modern UI** - Clean, professional interface with gradients
- **Smooth Animations** - Transitions and micro-interactions
- **Glass Morphism** - Modern backdrop blur effects
- **Progressive Disclosure** - Information revealed as needed
- **Depth & Shadows** - Visual hierarchy through layering

## 🎯 Learning Path

1. Start with **Dashboard** overview
2. Complete lessons **sequentially** (lessons unlock progressively)
3. Practice with **interactive exercises**
4. Review **mock test results** and coverage
5. Track **progress** and achievements

## 📱 Current Status

✅ **Frontend Complete** - Interactive course interface with mock data  
🚧 **Backend Integration** - Coming next for real test execution  
🚧 **Real Test Runner** - Will execute actual React Testing Library tests

## 🏆 Progress Tracking

- **Local Storage** persistence
- **Lesson completion** badges
- **Progress percentage** tracking
- **Skills mastered** counter
- **Time invested** metrics

Ready to master React Testing Library? Start your journey!

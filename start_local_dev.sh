#!/bin/bash

# RTL Course - Local Development Startup Script
# This script starts both the backend and frontend servers for local development

set -e  # Exit on any error

echo "🎓 RTL Course - Local Development Setup"
echo "======================================"

# Check if we're in the right directory
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    echo "❌ Error: Please run this script from the rtl-course root directory"
    echo "   Current directory: $(pwd)"
    echo "   Expected structure: rtl-course/backend and rtl-course/frontend"
    exit 1
fi

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check dependencies
echo "🔍 Checking dependencies..."

if ! command_exists python3; then
    echo "❌ Python 3 is required but not installed"
    exit 1
fi

if ! command_exists yarn; then
    echo "❌ Yarn is required but not installed"
    echo "   Install with: npm install -g yarn"
    exit 1
fi

echo "✅ Dependencies check passed"

# Install backend dependencies if needed
echo ""
echo "🐍 Setting up backend..."
cd backend

if [ ! -d "venv" ]; then
    echo "📦 Creating Python virtual environment..."
    python3 -m venv venv
fi

echo "🔧 Activating virtual environment and installing dependencies..."
source venv/bin/activate
pip install -r requirements.txt

echo "✅ Backend setup complete"
cd ..

# Install frontend dependencies if needed
echo ""
echo "⚛️  Setting up frontend..."
cd frontend

if [ ! -d "node_modules" ]; then
    echo "📦 Installing frontend dependencies..."
    yarn install
else
    echo "✅ Frontend dependencies already installed"
fi

echo "✅ Frontend setup complete"
cd ..

# Start the servers
echo ""
echo "🚀 Starting development servers..."
echo "   Backend:  http://localhost:8000"
echo "   Frontend: http://localhost:3000"
echo "   API Docs: http://localhost:8000/docs"
echo ""
echo "Press Ctrl+C to stop both servers"
echo "======================================"

# Function to cleanup background processes
cleanup() {
    echo ""
    echo "🛑 Stopping servers..."
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null || true
    wait $BACKEND_PID $FRONTEND_PID 2>/dev/null || true
    echo "👋 All servers stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start backend server in background
echo "🐍 Starting backend server..."
cd backend
source venv/bin/activate
python3 run_server.py &
BACKEND_PID=$!
cd ..

# Wait a moment for backend to start
sleep 3

# Start frontend server in background
echo "⚛️  Starting frontend server..."
cd frontend
yarn start &
FRONTEND_PID=$!
cd ..

# Wait for both processes
wait $BACKEND_PID $FRONTEND_PID

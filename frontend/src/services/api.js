import axios from 'axios';

const BACKEND_URL = process.env.REACT_APP_BACKEND_URL;
const API = `${BACKEND_URL}/api`;

class ApiService {
  constructor() {
    this.client = axios.create({
      baseURL: API,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        console.log(`API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('API Response Error:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // Health check
  async healthCheck() {
    try {
      const response = await this.client.get('/health');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Lesson endpoints
  async getAllLessons() {
    try {
      const response = await this.client.get('/lessons');
      return response.data.lessons;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getLesson(lessonId) {
    try {
      const response = await this.client.get(`/lessons/${lessonId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Progress endpoints
  async getProgress(userId = 'anonymous') {
    try {
      const response = await this.client.get(`/progress?user_id=${userId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async updateProgress(lessonId, completed = true, userId = 'anonymous') {
    try {
      const response = await this.client.post(`/progress?user_id=${userId}`, {
        lesson_id: lessonId,
        completed: completed
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Test execution endpoints
  async executeTest(lessonId, userCode) {
    try {
      const response = await this.client.post('/execute-test', {
        lesson_id: lessonId,
        user_code: userCode
      });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getTestExecution(executionId) {
    try {
      const response = await this.client.get(`/test-execution/${executionId}`);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // Error handling
  handleError(error) {
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      return {
        status,
        message: data.detail || data.message || 'An error occurred',
        data: data
      };
    } else if (error.request) {
      // Request was made but no response received
      return {
        status: 0,
        message: 'No response from server. Please check your connection.',
        data: null
      };
    } else {
      // Something else happened
      return {
        status: -1,
        message: error.message || 'An unexpected error occurred',
        data: null
      };
    }
  }
}

export const api = new ApiService();
export default api;
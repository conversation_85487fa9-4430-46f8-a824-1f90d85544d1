import React from "react";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "next-themes";
import { Toaster } from "./components/ui/toaster";
import CourseLayout from "./components/CourseLayout";
import Dashboard from "./components/Dashboard";
import Lesson from "./components/Lesson";
import "./App.css";

function App() {
  return (
    <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
      <div className="App min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<CourseLayout />}>
              <Route index element={<Dashboard />} />
              <Route path="lesson/:lessonId" element={<Lesson />} />
            </Route>
          </Routes>
        </BrowserRouter>
        <Toaster />
      </div>
    </ThemeProvider>
  );
}

export default App;
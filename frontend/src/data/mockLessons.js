export const mockLessons = [
  {
    id: 'accessibility-queries',
    title: 'Accessibility-First Queries',
    description: 'Learn to use queries that mirror how users interact with your app',
    difficulty: 'Beginner',
    estimatedTime: 25
  },
  {
    id: 'user-centric-testing',
    title: 'User-Centric Testing',
    description: 'Write tests from the user\'s perspective, not the developer\'s',
    difficulty: 'Beginner',
    estimatedTime: 25
  },
  {
    id: 'query-hierarchy',
    title: 'Query Priority Hierarchy',
    description: 'Master the recommended query order for better accessibility',
    difficulty: 'Intermediate',
    estimatedTime: 25
  },
  {
    id: 'simple-tests',
    title: 'Simple and Maintainable Tests',
    description: 'Keep tests straightforward and focused on essential functionality',
    difficulty: 'Beginner',
    estimatedTime: 25
  },
  {
    id: 'real-usage-patterns',
    title: 'Real Usage Patterns',
    description: 'Test components as they would actually be used in your app',
    difficulty: 'Intermediate',
    estimatedTime: 25
  },
  {
    id: 'minimal-mocking',
    title: 'Minimal Mocking',
    description: 'Only mock what\'s absolutely necessary for confident tests',
    difficulty: 'Advanced',
    estimatedTime: 25
  },
  {
    id: 'async-testing',
    title: 'Async Testing Done Right',
    description: 'Master waitFor, findBy queries, and async/await patterns',
    difficulty: 'Advanced',
    estimatedTime: 25
  }
];

export const getLessonContent = (lessonId) => {
  const lessons = {
    'accessibility-queries': {
      objective: 'Learn to prioritize queries that reflect how users actually interact with your application, focusing on accessibility and user experience.',
      concepts: [
        {
          title: 'getByRole - The Gold Standard',
          description: 'Query elements by their semantic role, which is how screen readers and other assistive technologies identify them.'
        },
        {
          title: 'getByLabelText - Form Interactions',
          description: 'Perfect for form elements, this query finds inputs by their associated labels, just like users do.'
        },
        {
          title: 'getByText - Visible Content',
          description: 'Query by the actual text users can see, making tests more intuitive and readable.'
        },
        {
          title: 'Avoid Implementation Details',
          description: 'Stay away from getByTestId, class names, or internal component structure that users cannot see.'
        }
      ],
      tip: 'If you can\'t find an element with getByRole, getByLabelText, or getByText, it might indicate an accessibility issue in your component.',
      badExample: `// ❌ Bad: Testing implementation details
const input = container.querySelector('.login-input');
const button = getByTestId('submit-btn');`,
      badExplanation: 'This approach tests implementation details (CSS classes, test IDs) that users don\'t interact with directly.',
      goodExample: `// ✅ Good: Testing like a user
const emailInput = getByLabelText(/email/i);
const passwordInput = getByLabelText(/password/i);
const submitButton = getByRole('button', { name: /sign in/i });`,
      goodExplanation: 'This approach mirrors how users find and interact with elements - by labels and roles, not implementation details.',
      exerciseDescription: 'Practice using accessibility-first queries to test a login form component.',
      exerciseInstructions: 'Rewrite the test using getByRole, getByLabelText, and getByText instead of implementation-specific queries.',
      initialCode: `import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import LoginForm from './LoginForm';

test('user can log in successfully', async () => {
  const user = userEvent.setup();
  render(<LoginForm />);
  
  // TODO: Replace these with accessibility-first queries
  const emailInput = screen.getByTestId('email-input');
  const passwordInput = screen.getByTestId('password-input');
  const submitButton = screen.getByTestId('login-button');
  
  await user.type(emailInput, '<EMAIL>');
  await user.type(passwordInput, 'password123');
  await user.click(submitButton);
  
  expect(screen.getByText(/welcome back/i)).toBeInTheDocument();
});`
    },
    'user-centric-testing': {
      objective: 'Focus on testing what users can see, click, type, and interact with rather than internal component state or implementation details.',
      concepts: [
        {
          title: 'Test User Interactions',
          description: 'Simulate real user actions like clicking, typing, and navigating rather than calling component methods directly.'
        },
        {
          title: 'Assert on Visible Outcomes',
          description: 'Check what users would see or experience, not internal state changes or prop updates.'
        },
        {
          title: 'Use Realistic Data',
          description: 'Test with data that resembles what users would actually encounter in your application.'
        },
        {
          title: 'Avoid Testing Implementation',
          description: 'Don\'t test how something works internally; test that it works correctly for users.'
        }
      ],
      tip: 'Ask yourself: "Would a real user care about this behavior?" If not, it might not need a test.',
      badExample: `// ❌ Bad: Testing internal state
const component = shallow(<Counter />);
expect(component.state('count')).toBe(0);
component.instance().increment();
expect(component.state('count')).toBe(1);`,
      badExplanation: 'This tests internal component state and methods that users never interact with directly.',
      goodExample: `// ✅ Good: Testing user interactions and visible outcomes
render(<Counter />);
const counter = screen.getByText('Count: 0');
const incrementButton = screen.getByRole('button', { name: /increment/i });

await user.click(incrementButton);
expect(screen.getByText('Count: 1')).toBeInTheDocument();`,
      goodExplanation: 'This tests the user\'s actual experience - what they see and what happens when they interact with the component.',
      exerciseDescription: 'Transform a component-focused test into a user-centric test.',
      exerciseInstructions: 'Rewrite the test to focus on user interactions and visible outcomes instead of component internals.',
      initialCode: `import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import TodoList from './TodoList';

test('can add and complete todos', async () => {
  const user = userEvent.setup();
  const mockAddTodo = jest.fn();
  const mockToggleTodo = jest.fn();
  
  render(<TodoList onAddTodo={mockAddTodo} onToggleTodo={mockToggleTodo} />);
  
  // TODO: Focus on user interactions instead of mock function calls
  const input = screen.getByLabelText(/new todo/i);
  const addButton = screen.getByRole('button', { name: /add/i });
  
  await user.type(input, 'Learn React Testing');
  await user.click(addButton);
  
  expect(mockAddTodo).toHaveBeenCalledWith('Learn React Testing');
  
  // TODO: Test what the user sees, not just function calls
});`
    },
    'query-hierarchy': {
      objective: 'Understand and apply the recommended query priority order to encourage better accessibility and more maintainable tests.',
      concepts: [
        {
          title: 'Queries Accessible to Everyone',
          description: 'getByRole, getByLabelText, getByPlaceholderText, getByText should be your first choice.'
        },
        {
          title: 'Semantic Queries',
          description: 'getByDisplayValue, getByAltText are good for specific semantic elements.'
        },
        {
          title: 'Test IDs - Last Resort',
          description: 'getByTestId should only be used when no other query makes sense, as it doesn\'t reflect user behavior.'
        },
        {
          title: 'Query Priority Benefits',
          description: 'Following the hierarchy ensures your tests encourage accessible markup and are more resilient to changes.'
        }
      ],
      tip: 'The Testing Library docs provide a helpful query priority guide. Bookmark it for reference!',
      badExample: `// ❌ Bad: Using test IDs first
const heading = screen.getByTestId('page-title');
const description = screen.getByTestId('page-description');
const button = screen.getByTestId('cta-button');`,
      badExplanation: 'This overuses test IDs instead of leveraging semantic HTML and accessibility features.',
      goodExample: `// ✅ Good: Following query hierarchy
const heading = screen.getByRole('heading', { name: /welcome/i });
const description = screen.getByText(/start your journey/i);
const button = screen.getByRole('button', { name: /get started/i });`,
      goodExplanation: 'This uses semantic queries that match how assistive technologies and users find elements.',
      exerciseDescription: 'Refactor queries to follow the recommended priority hierarchy.',
      exerciseInstructions: 'Replace the test ID queries with appropriate semantic queries following the recommended hierarchy.',
      initialCode: `import { render, screen } from '@testing-library/react';
import ProductCard from './ProductCard';

test('displays product information correctly', () => {
  const product = {
    name: 'Wireless Headphones',
    price: '$99.99',
    image: '/headphones.jpg',
    description: 'High-quality wireless headphones'
  };
  
  render(<ProductCard product={product} />);
  
  // TODO: Replace these with higher-priority queries
  expect(screen.getByTestId('product-name')).toHaveTextContent('Wireless Headphones');
  expect(screen.getByTestId('product-price')).toHaveTextContent('$99.99');
  expect(screen.getByTestId('product-image')).toHaveAttribute('src', '/headphones.jpg');
  expect(screen.getByTestId('add-to-cart')).toBeInTheDocument();
});`
    },
    'simple-tests': {
      objective: 'Learn to write straightforward, focused tests that are easy to understand, maintain, and debug.',
      concepts: [
        {
          title: 'One Behavior Per Test',
          description: 'Each test should verify a single piece of functionality to make failures easy to diagnose.'
        },
        {
          title: 'Clear Test Names',
          description: 'Test names should clearly describe what behavior is being tested from the user\'s perspective.'
        },
        {
          title: 'Minimal Setup',
          description: 'Only include the setup and data necessary for the specific test case.'
        },
        {
          title: 'Avoid Complex Logic',
          description: 'Tests should be straightforward to read and understand without complex conditionals or loops.'
        }
      ],
      tip: 'If your test is hard to understand or takes more than a few minutes to write, consider breaking it down into smaller, simpler tests.',
      badExample: `// ❌ Bad: Complex test doing too much
test('form validation and submission', async () => {
  const user = userEvent.setup();
  const mockSubmit = jest.fn();
  render(<ContactForm onSubmit={mockSubmit} />);
  
  // Test empty form
  await user.click(screen.getByRole('button', { name: /submit/i }));
  expect(screen.getByText(/name is required/i)).toBeInTheDocument();
  
  // Test invalid email
  await user.type(screen.getByLabelText(/email/i), 'invalid-email');
  await user.click(screen.getByRole('button', { name: /submit/i }));
  expect(screen.getByText(/invalid email/i)).toBeInTheDocument();
  
  // Test successful submission
  await user.clear(screen.getByLabelText(/email/i));
  await user.type(screen.getByLabelText(/name/i), 'John Doe');
  await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
  await user.click(screen.getByRole('button', { name: /submit/i }));
  expect(mockSubmit).toHaveBeenCalled();
});`,
      badExplanation: 'This test is trying to verify multiple behaviors in one test, making it hard to understand what failed if it breaks.',
      goodExample: `// ✅ Good: Simple, focused tests
test('shows validation error when name is empty', async () => {
  const user = userEvent.setup();
  render(<ContactForm />);
  
  await user.click(screen.getByRole('button', { name: /submit/i }));
  
  expect(screen.getByText(/name is required/i)).toBeInTheDocument();
});

test('submits form with valid data', async () => {
  const user = userEvent.setup();
  const mockSubmit = jest.fn();
  render(<ContactForm onSubmit={mockSubmit} />);
  
  await user.type(screen.getByLabelText(/name/i), 'John Doe');
  await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
  await user.click(screen.getByRole('button', { name: /submit/i }));
  
  expect(mockSubmit).toHaveBeenCalledWith({
    name: 'John Doe',
    email: '<EMAIL>'
  });
});`,
      goodExplanation: 'Each test has a single, clear purpose. If one fails, you know exactly what behavior is broken.',
      exerciseDescription: 'Break down a complex test into simpler, focused tests.',
      exerciseInstructions: 'Split the complex test into multiple simple tests, each focusing on one specific behavior.',
      initialCode: `import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import SearchForm from './SearchForm';

// TODO: Break this into multiple simple tests
test('search form functionality', async () => {
  const user = userEvent.setup();
  const mockSearch = jest.fn();
  render(<SearchForm onSearch={mockSearch} />);
  
  const input = screen.getByLabelText(/search/i);
  const button = screen.getByRole('button', { name: /search/i });
  
  // Test empty search
  await user.click(button);
  expect(screen.getByText(/please enter a search term/i)).toBeInTheDocument();
  
  // Test search with term
  await user.type(input, 'react testing');
  await user.click(button);
  expect(mockSearch).toHaveBeenCalledWith('react testing');
  
  // Test clear functionality
  const clearButton = screen.getByRole('button', { name: /clear/i });
  await user.click(clearButton);
  expect(input).toHaveValue('');
});`
    },
    'real-usage-patterns': {
      objective: 'Test components in realistic contexts with proper props, state, and environment rather than in artificial isolation.',
      concepts: [
        {
          title: 'Realistic Props and Data',
          description: 'Use data that resembles what your component would actually receive in production.'
        },
        {
          title: 'Proper Context Providers',
          description: 'Include necessary context providers, routers, and other dependencies your component needs.'
        },
        {
          title: 'Integration Over Isolation',
          description: 'Test components as part of a larger system rather than in complete isolation when possible.'
        },
        {
          title: 'Real User Flows',
          description: 'Test complete user journeys, not just individual component methods or props.'
        }
      ],
      tip: 'Create test utilities that wrap components with common providers and realistic data to make real usage testing easier.',
      badExample: `// ❌ Bad: Artificial isolation
test('user profile component', () => {
  render(<UserProfile user={{}} />);
  expect(screen.getByText('No user data')).toBeInTheDocument();
});`,
      badExplanation: 'This tests with unrealistic empty data that doesn\'t represent how users would actually encounter the component.',
      goodExample: `// ✅ Good: Realistic usage
test('displays user profile information', () => {
  const user = {
    id: 1,
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    avatar: '/avatars/sarah.jpg',
    joinDate: '2023-01-15',
    posts: 42
  };
  
  render(
    <Router>
      <AuthProvider value={{ currentUser: user }}>
        <UserProfile user={user} />
      </AuthProvider>
    </Router>
  );
  
  expect(screen.getByText('Sarah Johnson')).toBeInTheDocument();
  expect(screen.getByText('42 posts')).toBeInTheDocument();
  expect(screen.getByRole('img', { name: /sarah johnson/i })).toHaveAttribute('src', '/avatars/sarah.jpg');
});`,
      goodExplanation: 'This test uses realistic data and includes necessary providers, testing the component as users would actually encounter it.',
      exerciseDescription: 'Transform an isolated component test into a realistic usage pattern test.',
      exerciseInstructions: 'Add realistic data, necessary providers, and test the component in a more realistic context.',
      initialCode: `import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ShoppingCart from './ShoppingCart';

test('shopping cart component', async () => {
  const user = userEvent.setup();
  
  // TODO: Add realistic data and proper context
  render(<ShoppingCart items={[]} />);
  
  expect(screen.getByText(/your cart is empty/i)).toBeInTheDocument();
  
  // TODO: Test with actual cart items and user interactions
  // TODO: Include necessary providers (CartProvider, Router, etc.)
});`
    },
    'minimal-mocking': {
      objective: 'Learn when and how to use mocking judiciously to create confident tests without over-mocking dependencies.',
      concepts: [
        {
          title: 'Mock External Dependencies',
          description: 'Mock API calls, external services, and third-party libraries that are outside your control.'
        },
        {
          title: 'Keep Internal Code Real',
          description: 'Don\'t mock your own components, utilities, or business logic unless absolutely necessary.'
        },
        {
          title: 'Mock at the Boundary',
          description: 'Mock at the network/service boundary rather than deep within your application logic.'
        },
        {
          title: 'Test Real Integration',
          description: 'Prefer testing how your components work together rather than mocking every dependency.'
        }
      ],
      tip: 'Ask yourself: "Would mocking this help me catch real bugs, or would it hide integration issues?" Less mocking often means more confidence.',
      badExample: `// ❌ Bad: Over-mocking internal code
jest.mock('./UserService');
jest.mock('./ValidationUtils');
jest.mock('./DateFormatter');

test('user form submission', () => {
  UserService.createUser.mockResolvedValue({ id: 1 });
  ValidationUtils.validateEmail.mockReturnValue(true);
  DateFormatter.format.mockReturnValue('2023-01-01');
  
  // Test loses confidence because everything is mocked
});`,
      badExplanation: 'This mocks internal application code, which reduces confidence that the real integration works correctly.',
      goodExample: `// ✅ Good: Mock only external boundaries
import { rest } from 'msw';
import { setupServer } from 'msw/node';

const server = setupServer(
  rest.post('/api/users', (req, res, ctx) => {
    return res(ctx.json({ id: 1, name: 'John Doe' }));
  })
);

test('user form creates user successfully', async () => {
  const user = userEvent.setup();
  render(<UserForm />);
  
  await user.type(screen.getByLabelText(/name/i), 'John Doe');
  await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
  await user.click(screen.getByRole('button', { name: /create user/i }));
  
  expect(await screen.findByText(/user created successfully/i)).toBeInTheDocument();
});`,
      goodExplanation: 'This mocks only the external API, allowing all internal code (validation, formatting, etc.) to run normally.',
      exerciseDescription: 'Refactor an over-mocked test to use minimal, boundary-focused mocking.',
      exerciseInstructions: 'Replace internal mocks with real implementations and mock only the external API call.',
      initialCode: `import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import WeatherWidget from './WeatherWidget';

// TODO: Remove unnecessary mocks
jest.mock('./WeatherService');
jest.mock('./TemperatureConverter');
jest.mock('./LocationUtils');

test('displays weather information', async () => {
  const user = userEvent.setup();
  
  // TODO: Mock only the external weather API, not internal utilities
  WeatherService.getCurrentWeather.mockResolvedValue({
    temperature: 72,
    condition: 'sunny',
    humidity: 65
  });
  
  TemperatureConverter.toFahrenheit.mockReturnValue(72);
  LocationUtils.getCurrentLocation.mockResolvedValue({
    lat: 40.7128,
    lon: -74.0060
  });
  
  render(<WeatherWidget />);
  
  expect(await screen.findByText(/72°F/i)).toBeInTheDocument();
  expect(screen.getByText(/sunny/i)).toBeInTheDocument();
});`
    },
    'async-testing': {
      objective: 'Master the proper patterns for testing asynchronous behavior using waitFor, findBy queries, and async/await.',
      concepts: [
        {
          title: 'findBy Queries',
          description: 'Use findBy* queries for elements that appear asynchronously - they wait automatically and return promises.'
        },
        {
          title: 'waitFor Function',
          description: 'Use waitFor when you need to wait for multiple changes or complex async behavior.'
        },
        {
          title: 'Avoid act() Wrapper',
          description: 'Modern Testing Library handles act() automatically - avoid wrapping everything in act().'
        },
        {
          title: 'Proper Async/Await',
          description: 'Always await async operations and use proper error handling for failed assertions.'
        }
      ],
      tip: 'When testing async behavior, think about what the user would see and when. Use findBy for single elements and waitFor for complex conditions.',
      badExample: `// ❌ Bad: Using timeouts and act() wrappers
test('loads user data', async () => {
  render(<UserProfile userId="123" />);
  
  // Don't use arbitrary timeouts
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Don't wrap everything in act()
  await act(async () => {
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });
});`,
      badExplanation: 'This uses arbitrary timeouts and unnecessary act() wrappers, making tests slow and unreliable.',
      goodExample: `// ✅ Good: Using findBy and waitFor properly
test('loads and displays user data', async () => {
  render(<UserProfile userId="123" />);
  
  // findBy automatically waits for the element to appear
  expect(await screen.findByText('John Doe')).toBeInTheDocument();
  
  // waitFor when you need to wait for multiple conditions
  await waitFor(() => {
    expect(screen.getByText('Profile loaded')).toBeInTheDocument();
    expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
  });
});`,
      goodExplanation: 'This uses the appropriate async utilities that wait efficiently and fail with helpful error messages.',
      exerciseDescription: 'Fix async testing patterns using proper waitFor and findBy queries.',
      exerciseInstructions: 'Replace timeouts and manual act() calls with appropriate async testing utilities.',
      initialCode: `import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import DataTable from './DataTable';

test('loads and filters data', async () => {
  const user = userEvent.setup();
  render(<DataTable />);
  
  // TODO: Fix these async patterns
  
  // Bad: using setTimeout
  await new Promise(resolve => setTimeout(resolve, 2000));
  expect(screen.getByText('Row 1')).toBeInTheDocument();
  
  // Bad: unnecessary act() wrapper
  await act(async () => {
    await user.type(screen.getByLabelText(/filter/i), 'test');
  });
  
  // Bad: another timeout
  setTimeout(() => {
    expect(screen.getByText('Filtered results')).toBeInTheDocument();
  }, 1000);
  
  // TODO: Use proper async testing patterns
});`
    }
  };

  return lessons[lessonId] || null;
};
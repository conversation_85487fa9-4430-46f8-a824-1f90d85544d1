import { useState, useEffect } from 'react';
import { api } from '../services/api';
import { useToast } from './use-toast';

export const useApi = () => {
  const { toast } = useToast();

  const handleApiError = (error, defaultMessage = 'An error occurred') => {
    console.error('API Error:', error);
    
    const message = error.message || defaultMessage;
    toast({
      title: "Error",
      description: message,
      variant: "destructive",
    });
    
    return error;
  };

  return {
    api,
    handleApiError
  };
};

export const useLessons = () => {
  const [lessons, setLessons] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { handleApiError } = useApi();

  const fetchLessons = async () => {
    try {
      setLoading(true);
      setError(null);
      const lessonsData = await api.getAllLessons();
      setLessons(lessonsData);
    } catch (err) {
      const apiError = handleApiError(err, 'Failed to load lessons');
      setError(apiError);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLessons();
  }, []);

  return {
    lessons,
    loading,
    error,
    refetch: fetchLessons
  };
};

export const useLesson = (lessonId) => {
  const [lesson, setLesson] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { handleApiError } = useApi();

  const fetchLesson = async () => {
    if (!lessonId) return;
    
    try {
      setLoading(true);
      setError(null);
      const lessonData = await api.getLesson(lessonId);
      setLesson(lessonData);
    } catch (err) {
      const apiError = handleApiError(err, 'Failed to load lesson');
      setError(apiError);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLesson();
  }, [lessonId]);

  return {
    lesson,
    loading,
    error,
    refetch: fetchLesson
  };
};

export const useProgress = () => {
  const [progress, setProgress] = useState([]);
  const [stats, setStats] = useState({
    total_lessons: 0,
    completed_lessons: 0,
    progress_percentage: 0,
    total_attempts: 0,
    successful_tests: 0,
    time_invested: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { handleApiError } = useApi();

  const fetchProgress = async () => {
    try {
      setLoading(true);
      setError(null);
      const progressData = await api.getProgress();
      setProgress(progressData.progress);
      setStats(progressData.stats);
    } catch (err) {
      const apiError = handleApiError(err, 'Failed to load progress');
      setError(apiError);
    } finally {
      setLoading(false);
    }
  };

  const markLessonComplete = async (lessonId) => {
    try {
      await api.updateProgress(lessonId, true);
      await fetchProgress(); // Refresh progress
      return true;
    } catch (err) {
      handleApiError(err, 'Failed to update progress');
      return false;
    }
  };

  const isLessonComplete = (lessonId) => {
    return progress.some(p => p.lesson_id === lessonId && p.completed);
  };

  useEffect(() => {
    fetchProgress();
  }, []);

  // Transform stats for compatibility with existing UI
  const compatibleStats = {
    completedLessons: progress.filter(p => p.completed).map(p => p.lesson_id),
    totalLessons: stats.total_lessons,
    progress: Math.round(stats.progress_percentage)
  };

  return {
    ...compatibleStats,
    rawProgress: progress,
    stats,
    loading,
    error,
    markLessonComplete,
    isLessonComplete,
    refetch: fetchProgress
  };
};

export const useTestExecution = () => {
  const [executing, setExecuting] = useState(false);
  const [results, setResults] = useState(null);
  const [error, setError] = useState(null);
  const { handleApiError } = useApi();

  const executeTest = async (lessonId, userCode) => {
    try {
      setExecuting(true);
      setError(null);
      setResults(null);
      
      const executionResult = await api.executeTest(lessonId, userCode);
      setResults(executionResult);
      return executionResult;
    } catch (err) {
      const apiError = handleApiError(err, 'Failed to execute test');
      setError(apiError);
      return null;
    } finally {
      setExecuting(false);
    }
  };

  const clearResults = () => {
    setResults(null);
    setError(null);
  };

  return {
    executeTest,
    executing,
    results,
    error,
    clearResults
  };
};
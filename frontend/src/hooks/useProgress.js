import { useState, useEffect } from 'react';

export const useProgress = () => {
  // Initialize from localStorage or empty array
  const [completedLessons, setCompletedLessons] = useState(() => {
    const saved = localStorage.getItem('rtl-course-progress');
    return saved ? JSON.parse(saved) : [];
  });

  // Save to localStorage whenever completedLessons changes
  useEffect(() => {
    localStorage.setItem('rtl-course-progress', JSON.stringify(completedLessons));
  }, [completedLessons]);

  const totalLessons = 7; // Total number of lessons in the course
  const progress = (completedLessons.length / totalLessons) * 100;

  const markLessonComplete = (lessonId) => {
    setCompletedLessons(prev => {
      if (!prev.includes(lessonId)) {
        return [...prev, lessonId];
      }
      return prev;
    });
  };

  const isLessonComplete = (lessonId) => {
    return completedLessons.includes(lessonId);
  };

  const resetProgress = () => {
    setCompletedLessons([]);
  };

  return {
    completedLessons,
    totalLessons,
    progress: Math.round(progress),
    markLessonComplete,
    isLessonComplete,
    resetProgress
  };
};
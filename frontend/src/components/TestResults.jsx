import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from './ui/card';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';
import { 
  CheckCircle, 
  XCircle, 
  Loader2, 
  TestTube,
  BarChart3,
  AlertCircle
} from 'lucide-react';

export const TestResults = ({ results, isLoading }) => {
  if (isLoading) {
    return (
      <div className="space-y-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-center space-x-3">
              <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
              <span className="text-lg font-medium">Running tests...</span>
            </div>
            <div className="mt-4 space-y-2">
              <div className="h-2 bg-slate-200 rounded animate-pulse" />
              <div className="h-2 bg-slate-200 rounded animate-pulse w-3/4" />
              <div className="h-2 bg-slate-200 rounded animate-pulse w-1/2" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!results) {
    return (
      <div className="space-y-4">
        <Card>
          <CardContent className="p-6 text-center">
            <TestTube className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-600">No tests run yet</h3>
            <p className="text-slate-500 mt-2">Click "Run Tests" to see your results</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const passedTests = results.tests.filter(test => test.passed).length;
  const totalTests = results.tests.length;
  const passRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;

  return (
    <div className="space-y-4">
      {/* Overall Results */}
      <Card className={`border-2 ${results.passed ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            {results.passed ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <XCircle className="h-5 w-5 text-red-600" />
            )}
            <span className={results.passed ? 'text-green-800' : 'text-red-800'}>
              {results.passed ? 'Tests Passed!' : 'Tests Failed'}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-2xl font-bold">
                {passedTests}/{totalTests}
              </div>
              <div className="text-sm text-slate-600">Tests Passed</div>
            </div>
            <div>
              <div className="text-2xl font-bold">
                {Math.round(passRate)}%
              </div>
              <div className="text-sm text-slate-600">Success Rate</div>
            </div>
          </div>
          <Progress value={passRate} className="mt-4" />
          
          {results.execution_time && (
            <div className="mt-2 text-sm text-slate-600">
              Execution time: {(results.execution_time * 1000).toFixed(0)}ms
            </div>
          )}
        </CardContent>
      </Card>

      {/* Individual Test Results */}
      <Card>
        <CardHeader>
          <CardTitle>Test Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {results.tests.map((test, index) => (
            <div key={index} className="flex items-start space-x-3 p-3 rounded-lg border">
              {test.passed ? (
                <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
              )}
              <div className="flex-1">
                <div className="font-medium">{test.name}</div>
                <div className={`text-sm mt-1 ${test.passed ? 'text-green-700' : 'text-red-700'}`}>
                  {test.message}
                </div>
                {test.details && (
                  <div className="text-xs text-slate-500 mt-1">
                    {test.details}
                  </div>
                )}
              </div>
              <Badge variant={test.passed ? 'default' : 'destructive'}>
                {test.passed ? 'PASS' : 'FAIL'}
              </Badge>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Code Coverage */}
      {results.coverage && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Code Coverage</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Lines</span>
                  <span className="font-medium">{Math.round(results.coverage.lines)}%</span>
                </div>
                <Progress value={results.coverage.lines} />
              </div>
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Functions</span>
                  <span className="font-medium">{Math.round(results.coverage.functions)}%</span>
                </div>
                <Progress value={results.coverage.functions} />
              </div>
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span>Branches</span>
                  <span className="font-medium">{Math.round(results.coverage.branches)}%</span>
                </div>
                <Progress value={results.coverage.branches} />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {results.error && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-red-800">
              <AlertCircle className="h-5 w-5" />
              <span>Execution Error</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-sm text-red-700 whitespace-pre-wrap overflow-x-auto">
              {results.error}
            </pre>
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      {!results.passed && (
        <Card className="border-amber-200 bg-amber-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-amber-800">
              <AlertCircle className="h-5 w-5" />
              <span>Recommendations</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="text-sm text-amber-700 space-y-2">
              <li>• Check that your queries match the actual rendered elements</li>
              <li>• Ensure you're using accessibility-first queries like getByRole</li>
              <li>• Verify that async operations are properly awaited</li>
              <li>• Review the component structure and props being passed</li>
              <li>• Make sure all necessary imports are included</li>
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';
import { Alert, AlertDescription } from './ui/alert';
import { 
  BookOpen, 
  Trophy, 
  Target, 
  Clock,
  ArrowRight,
  CheckCircle,
  Star,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { useProgress, useLessons } from '../hooks/useApi';

const Dashboard = () => {
  const { 
    completedLessons, 
    totalLessons, 
    progress, 
    stats,
    loading: progressLoading 
  } = useProgress();
  
  const { 
    lessons, 
    loading: lessonsLoading, 
    error: lessonsError 
  } = useLessons();

  const isLoading = progressLoading || lessonsLoading;
  const nextLesson = lessons.find(lesson => !completedLessons.includes(lesson.id));

  if (lessonsError) {
    return (
      <div className="p-8 animate-in fade-in-50 duration-500">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load course data. Please check your connection and try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="p-8 flex items-center justify-center min-h-96">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
          <p className="text-slate-600">Loading course data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 space-y-8 animate-in fade-in-50 duration-500">
      {/* Welcome Section */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
          Master React Testing Library
        </h1>
        <p className="text-xl text-slate-600 max-w-3xl mx-auto">
          Learn industry best practices through interactive lessons and hands-on coding exercises
        </p>
      </div>

      {/* Progress Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="hover:shadow-lg transition-shadow duration-300">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Trophy className="h-5 w-5 text-amber-500" />
              <span>Progress</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>Completed</span>
                <span className="font-medium">{completedLessons.length}/{totalLessons}</span>
              </div>
              <Progress value={progress} className="h-2" />
              <p className="text-xs text-slate-500">
                {progress === 100 ? 'Course Complete! 🎉' : `${progress}% Complete`}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-300">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-green-500" />
              <span>Skills Mastered</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-green-600">
                {completedLessons.length}
              </div>
              <p className="text-sm text-slate-600">
                Core testing principles learned
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-shadow duration-300">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-blue-500" />
              <span>Time Invested</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-2xl font-bold text-blue-600">
                {stats.time_invested}m
              </div>
              <p className="text-sm text-slate-600">
                Estimated study time
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Next Lesson CTA */}
      {nextLesson && (
        <Card className="bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BookOpen className="h-6 w-6" />
              <span>Continue Learning</span>
            </CardTitle>
            <CardDescription className="text-blue-100">
              Ready for your next lesson?
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">{nextLesson.title}</h3>
                <p className="text-blue-100 text-sm mt-1">{nextLesson.description}</p>
                <Badge variant="secondary" className="mt-2">
                  {nextLesson.difficulty}
                </Badge>
              </div>
              <Link to={`/lesson/${nextLesson.id}`}>
                <Button variant="secondary" size="lg" className="group">
                  Start Lesson
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Course Overview */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-slate-800">Course Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {lessons.map((lesson, index) => {
            const isCompleted = completedLessons.includes(lesson.id);
            const isAccessible = index === 0 || completedLessons.includes(lessons[index - 1]?.id);
            
            return (
              <Card 
                key={lesson.id} 
                className={`hover:shadow-lg transition-all duration-300 ${
                  isCompleted ? 'ring-2 ring-green-200 bg-green-50' : ''
                } ${!isAccessible ? 'opacity-60' : 'hover:-translate-y-1'}`}
              >
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg flex items-center space-x-2">
                      {isCompleted && <CheckCircle className="h-5 w-5 text-green-500" />}
                      <span>{lesson.title}</span>
                    </CardTitle>
                    <Badge variant={
                      lesson.difficulty === 'Beginner' ? 'secondary' : 
                      lesson.difficulty === 'Intermediate' ? 'default' : 'destructive'
                    }>
                      {lesson.difficulty}
                    </Badge>
                  </div>
                  <CardDescription>{lesson.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm text-slate-500">
                      <span>~{lesson.estimated_time} min</span>
                      <span>•</span>
                      <span>Interactive</span>
                    </div>
                    <Link to={isAccessible ? `/lesson/${lesson.id}` : '#'}>
                      <Button 
                        variant={isCompleted ? 'secondary' : 'default'} 
                        size="sm"
                        disabled={!isAccessible}
                        className="group"
                      >
                        {isCompleted ? 'Review' : 'Start'}
                        <ArrowRight className="ml-2 h-3 w-3 group-hover:translate-x-1 transition-transform" />
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Course Features */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Star className="h-5 w-5 text-amber-500" />
            <span>What You'll Learn</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-semibold text-slate-800">Core Principles</h4>
              <ul className="space-y-1 text-sm text-slate-600">
                <li>• Accessibility-first testing approach</li>
                <li>• User-centric test writing</li>
                <li>• Query priority best practices</li>
                <li>• Maintainable test patterns</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold text-slate-800">Advanced Techniques</h4>
              <ul className="space-y-1 text-sm text-slate-600">
                <li>• Real usage pattern testing</li>
                <li>• Minimal mocking strategies</li>
                <li>• Async testing mastery</li>
                <li>• Integration test confidence</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Execution Stats */}
      {stats.total_attempts > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Testing Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">{stats.total_attempts}</div>
                <div className="text-sm text-slate-600">Total Tests</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">{stats.successful_tests}</div>
                <div className="text-sm text-slate-600">Passed</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-amber-600">
                  {stats.total_attempts > 0 ? Math.round((stats.successful_tests / stats.total_attempts) * 100) : 0}%
                </div>
                <div className="text-sm text-slate-600">Success Rate</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-600">{completedLessons.length}</div>
                <div className="text-sm text-slate-600">Mastered</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default Dashboard;
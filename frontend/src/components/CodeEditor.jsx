import React from 'react';
import Editor from '@monaco-editor/react';

export const CodeEditor = ({ value, onChange, language = 'javascript', theme = 'dark' }) => {
  const handleEditorChange = (newValue) => {
    onChange(newValue || '');
  };

  const editorOptions = {
    minimap: { enabled: false },
    scrollBeyondLastLine: false,
    fontSize: 14,
    lineHeight: 20,
    tabSize: 2,
    wordWrap: 'on',
    automaticLayout: true,
    scrollbar: {
      vertical: 'auto',
      horizontal: 'auto',
      verticalScrollbarSize: 8,
      horizontalScrollbarSize: 8,
    },
    suggest: {
      showKeywords: true,
      showSnippets: true,
    },
    quickSuggestions: {
      other: true,
      comments: true,
      strings: true,
    },
  };

  return (
    <div className="h-full">
      <Editor
        height="100%"
        language={language}
        value={value}
        onChange={handleEditorChange}
        theme={theme === 'dark' ? 'vs-dark' : 'light'}
        options={editorOptions}
        loading={
          <div className="flex items-center justify-center h-full">
            <div className="text-slate-400">Loading editor...</div>
          </div>
        }
      />
    </div>
  );
};
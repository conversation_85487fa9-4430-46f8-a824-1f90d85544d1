import React from 'react';
import { NavLink } from 'react-router-dom';
import { Badge } from './ui/badge';
import { Alert, AlertDescription } from './ui/alert';
import { 
  CheckCircle, 
  Circle, 
  Play,
  Eye,
  Users,
  List,
  TestTube,
  Target,
  Shield,
  Clock,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { useProgress, useLessons } from '../hooks/useApi';

const lessonIcons = {
  'accessibility-queries': Eye,
  'user-centric-testing': Users,
  'query-hierarchy': List,
  'simple-tests': TestTube,
  'real-usage-patterns': Target,
  'minimal-mocking': Shield,
  'async-testing': Clock
};

export const Sidebar = () => {
  const { completedLessons, loading: progressLoading } = useProgress();
  const { lessons, loading: lessonsLoading, error } = useLessons();

  const isLoading = progressLoading || lessonsLoading;

  if (error) {
    return (
      <aside className="w-80 bg-white/70 backdrop-blur-md border-r border-slate-200 p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-xs">
            Failed to load lessons
          </AlertDescription>
        </Alert>
      </aside>
    );
  }

  if (isLoading) {
    return (
      <aside className="w-80 bg-white/70 backdrop-blur-md border-r border-slate-200 p-6">
        <div className="flex items-center justify-center py-8">
          <div className="text-center space-y-2">
            <Loader2 className="h-6 w-6 animate-spin mx-auto text-blue-600" />
            <p className="text-sm text-slate-600">Loading...</p>
          </div>
        </div>
      </aside>
    );
  }

  return (
    <aside className="w-80 bg-white/70 backdrop-blur-md border-r border-slate-200 p-6 overflow-y-auto">
      <div className="space-y-6">
        <div>
          <h2 className="text-lg font-semibold text-slate-800 mb-4">Course Contents</h2>
          <div className="space-y-2">
            {lessons.map((lesson, index) => {
              const Icon = lessonIcons[lesson.id] || Play;
              const isCompleted = completedLessons.includes(lesson.id);
              const isAccessible = index === 0 || completedLessons.includes(lessons[index - 1]?.id);
              
              return (
                <NavLink
                  key={lesson.id}
                  to={`/lesson/${lesson.id}`}
                  className={({ isActive }) =>
                    `flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 group ${
                      isActive
                        ? 'bg-blue-100 text-blue-700 shadow-sm'
                        : isAccessible
                        ? 'hover:bg-slate-100 text-slate-700'
                        : 'text-slate-400 cursor-not-allowed'
                    }`
                  }
                  onClick={(e) => !isAccessible && e.preventDefault()}
                >
                  <div className="flex-shrink-0">
                    {isCompleted ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : isAccessible ? (
                      <Icon className="h-5 w-5" />
                    ) : (
                      <Circle className="h-5 w-5" />
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium truncate">{lesson.title}</span>
                      <Badge 
                        variant={lesson.difficulty === 'Beginner' ? 'secondary' : lesson.difficulty === 'Intermediate' ? 'default' : 'destructive'}
                        className="ml-2 text-xs"
                      >
                        {lesson.difficulty}
                      </Badge>
                    </div>
                    <p className="text-xs text-slate-500 mt-1 line-clamp-2">{lesson.description}</p>
                  </div>
                </NavLink>
              );
            })}
          </div>
        </div>
        
        <div className="border-t border-slate-200 pt-6">
          <h3 className="text-sm font-semibold text-slate-800 mb-3">Quick Links</h3>
          <div className="space-y-2">
            <NavLink 
              to="/" 
              className={({ isActive }) =>
                `flex items-center space-x-3 p-2 rounded-lg transition-colors ${
                  isActive ? 'bg-blue-100 text-blue-700' : 'hover:bg-slate-100 text-slate-600'
                }`
              }
            >
              <Play className="h-4 w-4" />
              <span className="text-sm">Dashboard</span>
            </NavLink>
          </div>
        </div>
      </div>
    </aside>
  );
};
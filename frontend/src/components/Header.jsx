import React from 'react';
import { Badge } from './ui/badge';
import { Progress } from './ui/progress';
import { 
  CheckCircle, 
  Trophy, 
  User
} from 'lucide-react';
import { useProgress } from '../hooks/useApi';

export const Header = () => {
  const { completedLessons, totalLessons, progress } = useProgress();

  return (
    <header className="bg-white/80 backdrop-blur-md border-b border-slate-200 px-6 py-4 sticky top-0 z-50">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="h-8 w-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">RTL</span>
            </div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              RTL Mastery
            </h1>
          </div>
          <Badge variant="secondary" className="animate-pulse">
            Interactive Course
          </Badge>
        </div>
        
        <div className="flex items-center space-x-6">
          <div className="flex items-center space-x-3">
            <Trophy className="h-5 w-5 text-amber-500" />
            <span className="text-sm font-medium text-slate-600">
              {completedLessons.length}/{totalLessons} Lessons
            </span>
            <div className="w-32">
              <Progress value={progress} className="h-2" />
            </div>
          </div>
          
          <div className="flex items-center space-x-2 px-3 py-1 rounded-lg bg-slate-100">
            <User className="h-4 w-4 text-slate-500" />
            <span className="text-sm text-slate-600">Anonymous</span>
          </div>
        </div>
      </div>
    </header>
  );
};
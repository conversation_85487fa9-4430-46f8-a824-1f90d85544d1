import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Alert, AlertDescription } from './ui/alert';
import { CodeEditor } from './CodeEditor';
import { TestResults } from './TestResults';
import { 
  BookOpen, 
  Play, 
  CheckCircle, 
  ArrowLeft, 
  ArrowRight,
  Lightbulb,
  Target,
  Code,
  TestTube,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { useProgress, useLesson, useLessons, useTestExecution } from '../hooks/useApi';
import { useToast } from '../hooks/use-toast';

const Lesson = () => {
  const { lessonId } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  // API hooks
  const { markLessonComplete, isLessonComplete } = useProgress();
  const { lesson, loading: lessonLoading, error: lessonError } = useLesson(lessonId);
  const { lessons, loading: lessonsLoading } = useLessons();
  const { executeTest, executing, results, clearResults } = useTestExecution();
  
  // Local state
  const [code, setCode] = useState('');
  const [activeTab, setActiveTab] = useState('lesson');
  
  const isLoading = lessonLoading || lessonsLoading;
  const currentIndex = lessons.findIndex(l => l.id === lessonId);
  const nextLesson = lessons[currentIndex + 1];
  const prevLesson = lessons[currentIndex - 1];
  const isCompleted = isLessonComplete(lessonId);

  useEffect(() => {
    if (lesson?.content?.initial_code) {
      setCode(lesson.content.initial_code);
    }
  }, [lesson]);

  const handleRunTests = async () => {
    if (!code.trim()) {
      toast({
        title: "No Code",
        description: "Please write some test code before running tests.",
        variant: "destructive",
      });
      return;
    }

    setActiveTab('results');
    clearResults();
    
    const result = await executeTest(lessonId, code);
    if (result && result.passed) {
      toast({
        title: "Tests Passed! 🎉",
        description: "Great job! Your tests are working correctly.",
      });
    }
  };

  const handleCompleteLesson = async () => {
    const success = await markLessonComplete(lessonId);
    if (success) {
      toast({
        title: "Lesson Complete! 🎉",
        description: `You've mastered ${lesson.title}`,
      });
    }
  };

  if (lessonError) {
    return (
      <div className="p-8 text-center">
        <Alert variant="destructive" className="max-w-md mx-auto">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load lesson. Please check your connection and try again.
          </AlertDescription>
        </Alert>
        <Link to="/" className="mt-4 inline-block">
          <Button>Return to Dashboard</Button>
        </Link>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="p-8 flex items-center justify-center min-h-96">
        <div className="text-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
          <p className="text-slate-600">Loading lesson...</p>
        </div>
      </div>
    );
  }

  if (!lesson) {
    return (
      <div className="p-8 text-center">
        <h2 className="text-2xl font-bold text-slate-800 mb-4">Lesson Not Found</h2>
        <Link to="/">
          <Button>Return to Dashboard</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="flex h-full">
      {/* Left Panel - Content */}
      <div className="w-1/2 p-6 overflow-y-auto bg-white/50 backdrop-blur-sm">
        <div className="space-y-6">
          {/* Header */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 text-sm text-slate-500">
              <Link to="/" className="hover:text-blue-600 transition-colors">Dashboard</Link>
              <span>/</span>
              <span>{lesson.title}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-slate-800">{lesson.title}</h1>
                <p className="text-slate-600 mt-2">{lesson.description}</p>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant={
                  lesson.difficulty === 'Beginner' ? 'secondary' : 
                  lesson.difficulty === 'Intermediate' ? 'default' : 'destructive'
                }>
                  {lesson.difficulty}
                </Badge>
                {isCompleted && (
                  <Badge variant="outline" className="text-green-600 border-green-600">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Complete
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {/* Lesson Content */}
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="lesson" className="text-sm">
                <BookOpen className="h-4 w-4 mr-2" />
                Learn
              </TabsTrigger>
              <TabsTrigger value="example" className="text-sm">
                <Lightbulb className="h-4 w-4 mr-2" />
                Example
              </TabsTrigger>
              <TabsTrigger value="practice" className="text-sm">
                <Code className="h-4 w-4 mr-2" />
                Practice
              </TabsTrigger>
              <TabsTrigger value="results" className="text-sm">
                <TestTube className="h-4 w-4 mr-2" />
                Results
              </TabsTrigger>
            </TabsList>

            <TabsContent value="lesson" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Target className="h-5 w-5 text-blue-600" />
                    <span>Learning Objective</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-slate-700">{lesson.content.objective}</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Key Concepts</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {lesson.content.concepts.map((concept, index) => (
                    <div key={index} className="border-l-4 border-blue-200 pl-4">
                      <h4 className="font-semibold text-slate-800">{concept.title}</h4>
                      <p className="text-slate-600 text-sm mt-1">{concept.description}</p>
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Alert>
                <Lightbulb className="h-4 w-4" />
                <AlertDescription>
                  <strong>Pro Tip:</strong> {lesson.content.tip}
                </AlertDescription>
              </Alert>
            </TabsContent>

            <TabsContent value="example" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>❌ What NOT to do</CardTitle>
                  <CardDescription>This example shows common anti-patterns</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <pre className="text-sm text-red-800 overflow-x-auto whitespace-pre-wrap">
                      <code>{lesson.content.bad_example}</code>
                    </pre>
                  </div>
                  <p className="text-sm text-slate-600 mt-2">{lesson.content.bad_explanation}</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>✅ Best Practice</CardTitle>
                  <CardDescription>This example demonstrates the correct approach</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <pre className="text-sm text-green-800 overflow-x-auto whitespace-pre-wrap">
                      <code>{lesson.content.good_example}</code>
                    </pre>
                  </div>
                  <p className="text-sm text-slate-600 mt-2">{lesson.content.good_explanation}</p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="practice">
              <Card>
                <CardHeader>
                  <CardTitle>Practice Exercise</CardTitle>
                  <CardDescription>{lesson.content.exercise_description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-slate-600 mb-4">{lesson.content.exercise_instructions}</p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="results">
              <TestResults results={results} isLoading={executing} />
            </TabsContent>
          </Tabs>

          {/* Navigation */}
          <div className="flex items-center justify-between pt-6 border-t border-slate-200">
            <div>
              {prevLesson && (
                <Link to={`/lesson/${prevLesson.id}`}>
                  <Button variant="outline" className="group">
                    <ArrowLeft className="h-4 w-4 mr-2 group-hover:-translate-x-1 transition-transform" />
                    Previous
                  </Button>
                </Link>
              )}
            </div>
            
            <div className="flex items-center space-x-2">
              {!isCompleted && (
                <Button onClick={handleCompleteLesson} variant="secondary">
                  Mark Complete
                </Button>
              )}
              {nextLesson && (
                <Link to={`/lesson/${nextLesson.id}`}>
                  <Button className="group">
                    Next Lesson
                    <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Right Panel - Code Editor */}
      <div className="w-1/2 border-l border-slate-200 bg-slate-900">
        <div className="h-full flex flex-col">
          <div className="bg-slate-800 px-4 py-3 border-b border-slate-700">
            <div className="flex items-center justify-between">
              <h3 className="text-white font-medium">Code Editor</h3>
              <Button 
                onClick={handleRunTests} 
                disabled={executing}
                size="sm"
                className="bg-green-600 hover:bg-green-700"
              >
                {executing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Running...
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Run Tests
                  </>
                )}
              </Button>
            </div>
          </div>
          
          <div className="flex-1">
            <CodeEditor
              value={code}
              onChange={setCode}
              language="javascript"
              theme="dark"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Lesson;
"""
Local development server for RTL Course.
This version uses a mock database instead of MongoDB for easier local development.
"""

import logging
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, APIRouter
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Dict, Any, Optional
import asyncio
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Mock Database Implementation (from test_backend_integration.py)
class MockDatabase:
    def __init__(self):
        self.lessons = [
            {
                "id": "lesson-1",
                "title": "Introduction to React Testing Library",
                "description": "Learn the basics of RTL and accessibility-first testing",
                "content": "React Testing Library is a simple and complete testing utilities that encourage good testing practices...",
                "difficulty": "beginner",
                "order": 1,
                "exercises": [
                    {
                        "id": "exercise-1",
                        "title": "Your First RTL Test",
                        "description": "Write a simple test using getByRole",
                        "starter_code": "import { render, screen } from '@testing-library/react';\n\ntest('should render button', () => {\n  // Your code here\n});",
                        "solution": "import { render, screen } from '@testing-library/react';\n\ntest('should render button', () => {\n  render(<button>Click me</button>);\n  expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument();\n});",
                        "hints": ["Use getByRole to find the button", "Remember to use case-insensitive matching"]
                    }
                ]
            },
            {
                "id": "lesson-2", 
                "title": "Accessibility-First Queries",
                "description": "Master getByRole, getByLabelText, and other accessible queries",
                "content": "RTL encourages testing in a way that resembles how users interact with your app...",
                "difficulty": "intermediate",
                "order": 2,
                "exercises": [
                    {
                        "id": "exercise-2",
                        "title": "Form Testing with Labels",
                        "description": "Test form inputs using accessible queries",
                        "starter_code": "import { render, screen } from '@testing-library/react';\n\ntest('should handle form input', () => {\n  // Your code here\n});",
                        "solution": "import { render, screen } from '@testing-library/react';\n\ntest('should handle form input', () => {\n  render(<form><label htmlFor='email'>Email</label><input id='email' type='email' /></form>);\n  expect(screen.getByLabelText(/email/i)).toBeInTheDocument();\n});",
                        "hints": ["Use getByLabelText for form inputs", "Make sure labels are properly associated"]
                    }
                ]
            },
            {
                "id": "lesson-3",
                "title": "User Interactions and Events", 
                "description": "Learn to test user interactions with userEvent and fireEvent",
                "content": "Testing user interactions is crucial for ensuring your app works as expected...",
                "difficulty": "intermediate",
                "order": 3,
                "exercises": [
                    {
                        "id": "exercise-3",
                        "title": "Click Events",
                        "description": "Test button click interactions",
                        "starter_code": "import { render, screen } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\n\ntest('should handle click', async () => {\n  // Your code here\n});",
                        "solution": "import { render, screen } from '@testing-library/react';\nimport userEvent from '@testing-library/user-event';\n\ntest('should handle click', async () => {\n  const user = userEvent.setup();\n  const handleClick = jest.fn();\n  render(<button onClick={handleClick}>Click me</button>);\n  await user.click(screen.getByRole('button'));\n  expect(handleClick).toHaveBeenCalledTimes(1);\n});",
                        "hints": ["Use userEvent.setup() for modern user interactions", "Remember to await user interactions"]
                    }
                ]
            }
        ]
        
        self.progress = {}
        self.test_results = {}
    
    async def get_lessons(self) -> List[Dict[str, Any]]:
        return self.lessons
    
    async def get_lesson(self, lesson_id: str) -> Optional[Dict[str, Any]]:
        for lesson in self.lessons:
            if lesson["id"] == lesson_id:
                return lesson
        return None
    
    async def get_user_progress(self, user_id: str) -> Dict[str, Any]:
        return self.progress.get(user_id, {
            "user_id": user_id,
            "completed_lessons": [],
            "completed_exercises": [],
            "total_score": 0,
            "last_activity": datetime.now().isoformat()
        })
    
    async def update_user_progress(self, user_id: str, lesson_id: str, exercise_id: str, score: int) -> Dict[str, Any]:
        if user_id not in self.progress:
            self.progress[user_id] = {
                "user_id": user_id,
                "completed_lessons": [],
                "completed_exercises": [],
                "total_score": 0,
                "last_activity": datetime.now().isoformat()
            }
        
        progress = self.progress[user_id]
        exercise_key = f"{lesson_id}:{exercise_id}"
        
        if exercise_key not in progress["completed_exercises"]:
            progress["completed_exercises"].append(exercise_key)
            progress["total_score"] += score
        
        if lesson_id not in progress["completed_lessons"]:
            # Check if all exercises in lesson are completed
            lesson = await self.get_lesson(lesson_id)
            if lesson:
                lesson_exercises = [f"{lesson_id}:{ex['id']}" for ex in lesson.get("exercises", [])]
                if all(ex in progress["completed_exercises"] for ex in lesson_exercises):
                    progress["completed_lessons"].append(lesson_id)
        
        progress["last_activity"] = datetime.now().isoformat()
        return progress
    
    async def save_test_result(self, user_id: str, lesson_id: str, exercise_id: str, result: Dict[str, Any]) -> Dict[str, Any]:
        key = f"{user_id}:{lesson_id}:{exercise_id}"
        self.test_results[key] = {
            **result,
            "timestamp": datetime.now().isoformat()
        }
        return self.test_results[key]

# Initialize mock database
db = MockDatabase()

# Create FastAPI app
app = FastAPI(
    title="RTL Course API (Local Development)",
    description="React Testing Library Course API - Local Development Version with Mock Database",
    version="1.0.0"
)

# Create API router
api_router = APIRouter(prefix="/api")

@api_router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "message": "RTL Course API is running (Local Development Mode)",
        "test_environment": True
    }

@api_router.get("/lessons")
async def get_lessons():
    """Get all lessons"""
    try:
        lessons = await db.get_lessons()
        logger.info(f"Retrieved {len(lessons)} lessons")
        return lessons
    except Exception as e:
        logger.error(f"Error fetching lessons: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch lessons")

@api_router.get("/lessons/{lesson_id}")
async def get_lesson(lesson_id: str):
    """Get a specific lesson"""
    try:
        lesson = await db.get_lesson(lesson_id)
        if not lesson:
            raise HTTPException(status_code=404, detail="Lesson not found")
        return lesson
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching lesson {lesson_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch lesson")

@api_router.get("/progress/{user_id}")
async def get_progress(user_id: str):
    """Get user progress"""
    try:
        progress = await db.get_user_progress(user_id)
        return progress
    except Exception as e:
        logger.error(f"Error fetching progress for user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch progress")

@api_router.post("/progress/{user_id}")
async def update_progress(user_id: str, data: dict):
    """Update user progress"""
    try:
        lesson_id = data.get("lesson_id")
        exercise_id = data.get("exercise_id") 
        score = data.get("score", 0)
        
        if not lesson_id or not exercise_id:
            raise HTTPException(status_code=400, detail="lesson_id and exercise_id are required")
        
        progress = await db.update_user_progress(user_id, lesson_id, exercise_id, score)
        return progress
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating progress for user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to update progress")

@api_router.post("/test/run")
async def run_test(data: dict):
    """Run RTL test and return results"""
    try:
        user_id = data.get("user_id", "anonymous")
        lesson_id = data.get("lesson_id")
        exercise_id = data.get("exercise_id")
        code = data.get("code", "")
        
        if not lesson_id or not exercise_id:
            raise HTTPException(status_code=400, detail="lesson_id and exercise_id are required")
        
        # Import and use the test runner
        from test_runner import RTLTestRunner
        
        runner = RTLTestRunner()
        result = await runner.run_test(code, lesson_id, exercise_id)
        
        # Save test result
        await db.save_test_result(user_id, lesson_id, exercise_id, result)
        
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error running test: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to run test: {str(e)}")

# Include the router in the main app
app.include_router(api_router)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

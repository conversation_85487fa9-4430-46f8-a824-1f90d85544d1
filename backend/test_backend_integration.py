import pytest
import asyncio
from httpx import AsyncClient
from fastapi.testclient import TestClient
import sys
import os

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server import app
from database import db

# Test client for synchronous testing
client = TestClient(app)

class TestRTLCourseAPI:
    """Test suite for RTL Course API endpoints"""
    
    def test_health_check(self):
        """Test health check endpoint"""
        response = client.get("/api/health")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert data["status"] == "healthy"
        print("✅ Health check endpoint working")
    
    def test_root_endpoint(self):
        """Test root API endpoint"""
        response = client.get("/api/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        print("✅ Root endpoint working")
    
    def test_get_all_lessons(self):
        """Test fetching all lessons"""
        response = client.get("/api/lessons")
        assert response.status_code == 200
        data = response.json()
        assert "lessons" in data
        assert isinstance(data["lessons"], list)
        assert len(data["lessons"]) > 0
        
        # Verify lesson structure
        lesson = data["lessons"][0]
        required_fields = ["id", "title", "description", "difficulty", "estimated_time", "content", "order"]
        for field in required_fields:
            assert field in lesson, f"Missing field: {field}"
        
        # Verify content structure
        content = lesson["content"]
        content_fields = ["objective", "concepts", "tip", "bad_example", "good_example", "exercise_description", "initial_code"]
        for field in content_fields:
            assert field in content, f"Missing content field: {field}"
        
        print(f"✅ Lessons endpoint working - found {len(data['lessons'])} lessons")
    
    def test_get_specific_lesson(self):
        """Test fetching a specific lesson"""
        # First get all lessons to get a valid ID
        response = client.get("/api/lessons")
        lessons = response.json()["lessons"]
        lesson_id = lessons[0]["id"]
        
        # Test fetching specific lesson
        response = client.get(f"/api/lessons/{lesson_id}")
        assert response.status_code == 200
        lesson = response.json()
        assert lesson["id"] == lesson_id
        print(f"✅ Specific lesson endpoint working - lesson: {lesson['title']}")
    
    def test_get_nonexistent_lesson(self):
        """Test fetching non-existent lesson"""
        response = client.get("/api/lessons/nonexistent-lesson")
        assert response.status_code == 404
        print("✅ Non-existent lesson handling working")
    
    def test_get_user_progress(self):
        """Test fetching user progress"""
        response = client.get("/api/progress")
        assert response.status_code == 200
        data = response.json()
        assert "progress" in data
        assert "stats" in data
        assert isinstance(data["progress"], list)
        
        # Verify stats structure
        stats = data["stats"]
        stats_fields = ["total_lessons", "completed_lessons", "progress_percentage", "total_attempts", "successful_tests", "time_invested"]
        for field in stats_fields:
            assert field in stats, f"Missing stats field: {field}"
        
        print("✅ Progress endpoint working")
    
    def test_update_progress(self):
        """Test updating user progress"""
        # First get a lesson ID
        response = client.get("/api/lessons")
        lessons = response.json()["lessons"]
        lesson_id = lessons[0]["id"]
        
        # Update progress
        response = client.post("/api/progress", json={
            "lesson_id": lesson_id,
            "completed": True
        })
        assert response.status_code == 200
        progress = response.json()
        assert progress["lesson_id"] == lesson_id
        assert progress["completed"] == True
        print(f"✅ Progress update working - marked lesson {lesson_id} complete")
    
    def test_test_execution_endpoint(self):
        """Test the test execution endpoint"""
        # Get a lesson ID
        response = client.get("/api/lessons")
        lessons = response.json()["lessons"]
        lesson_id = lessons[0]["id"]
        
        # Test with sample code
        test_code = """
import { render, screen } from '@testing-library/react';
import LoginForm from './LoginForm';

test('renders login form', () => {
  render(<LoginForm />);
  expect(screen.getByText(/login/i)).toBeInTheDocument();
});
"""
        
        response = client.post("/api/execute-test", json={
            "lesson_id": lesson_id,
            "user_code": test_code
        })
        
        # The test might fail due to Jest setup issues, but endpoint should work
        assert response.status_code == 200
        result = response.json()
        
        # Verify response structure
        expected_fields = ["id", "lesson_id", "user_code", "passed", "tests", "coverage", "execution_time"]
        for field in expected_fields:
            assert field in result, f"Missing field: {field}"
        
        assert result["lesson_id"] == lesson_id
        assert result["user_code"] == test_code
        assert isinstance(result["tests"], list)
        assert isinstance(result["coverage"], dict)
        
        print(f"✅ Test execution endpoint working - returned {'PASS' if result['passed'] else 'FAIL'}")
    
    def test_invalid_test_execution(self):
        """Test test execution with invalid lesson ID"""
        response = client.post("/api/execute-test", json={
            "lesson_id": "invalid-lesson",
            "user_code": "test code"
        })
        assert response.status_code == 404
        print("✅ Invalid test execution handling working")
    
    def test_cors_headers(self):
        """Test CORS headers are present"""
        response = client.get("/api/health")
        # FastAPI's CORS middleware should add these headers
        print("✅ CORS middleware configured")
    
    def test_database_connection(self):
        """Test database connectivity through API"""
        # This is implicit in the lessons test, but we'll verify explicitly
        response = client.get("/api/lessons")
        assert response.status_code == 200
        data = response.json()
        assert len(data["lessons"]) > 0
        print("✅ Database connection working through API")

def run_comprehensive_tests():
    """Run all tests and report results"""
    print("🚀 Starting comprehensive RTL Course API testing...\n")
    
    test_class = TestRTLCourseAPI()
    test_methods = [method for method in dir(test_class) if method.startswith('test_')]
    
    passed = 0
    failed = 0
    
    for test_method in test_methods:
        try:
            method = getattr(test_class, test_method)
            method()
            passed += 1
        except Exception as e:
            print(f"❌ {test_method} failed: {str(e)}")
            failed += 1
    
    print(f"\n📊 Test Results:")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 All tests passed! Backend integration is working perfectly.")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Check the errors above.")
    
    return failed == 0

if __name__ == "__main__":
    success = run_comprehensive_tests()
    exit(0 if success else 1)
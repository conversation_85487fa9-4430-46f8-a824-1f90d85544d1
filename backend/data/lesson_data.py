from typing import List
from models import Lesson, <PERSON>on<PERSON>ontent, LessonConcept, DifficultyLevel

def get_all_lessons() -> List[Lesson]:
    """Get all lesson data"""
    
    lessons = [
        Lesson(
            id="accessibility-queries",
            title="Accessibility-First Queries",
            description="Learn to use queries that mirror how users interact with your app",
            difficulty=DifficultyLevel.BEGINNER,
            estimated_time=25,
            order=1,
            content=LessonContent(
                objective="Learn to prioritize queries that reflect how users actually interact with your application, focusing on accessibility and user experience.",
                concepts=[
                    LessonConcept(
                        title="getByRole - The Gold Standard",
                        description="Query elements by their semantic role, which is how screen readers and other assistive technologies identify them."
                    ),
                    LessonConcept(
                        title="getByLabelText - Form Interactions",
                        description="Perfect for form elements, this query finds inputs by their associated labels, just like users do."
                    ),
                    LessonConcept(
                        title="getByText - Visible Content",
                        description="Query by the actual text users can see, making tests more intuitive and readable."
                    ),
                    LessonConcept(
                        title="Avoid Implementation Details",
                        description="Stay away from getByTestId, class names, or internal component structure that users cannot see."
                    )
                ],
                tip="If you can't find an element with getByRole, getByLabelText, or getByText, it might indicate an accessibility issue in your component.",
                bad_example="""// ❌ Bad: Testing implementation details
const input = container.querySelector('.login-input');
const button = getByTestId('submit-btn');""",
                bad_explanation="This approach tests implementation details (CSS classes, test IDs) that users don't interact with directly.",
                good_example="""// ✅ Good: Testing like a user
const emailInput = getByLabelText(/email/i);
const passwordInput = getByLabelText(/password/i);
const submitButton = getByRole('button', { name: /sign in/i });""",
                good_explanation="This approach mirrors how users find and interact with elements - by labels and roles, not implementation details.",
                exercise_description="Practice using accessibility-first queries to test a login form component.",
                exercise_instructions="Rewrite the test using getByRole, getByLabelText, and getByText instead of implementation-specific queries.",
                initial_code="""import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import LoginForm from './LoginForm';

test('user can log in successfully', async () => {
  const user = userEvent.setup();
  render(<LoginForm />);
  
  // TODO: Replace these with accessibility-first queries
  const emailInput = screen.getByTestId('email-input');
  const passwordInput = screen.getByTestId('password-input');
  const submitButton = screen.getByTestId('login-button');
  
  await user.type(emailInput, '<EMAIL>');
  await user.type(passwordInput, 'password123');
  await user.click(submitButton);
  
  expect(screen.getByText(/welcome back/i)).toBeInTheDocument();
});"""
            )
        ),
        
        Lesson(
            id="user-centric-testing",
            title="User-Centric Testing",
            description="Write tests from the user's perspective, not the developer's",
            difficulty=DifficultyLevel.BEGINNER,
            estimated_time=25,
            order=2,
            content=LessonContent(
                objective="Focus on testing what users can see, click, type, and interact with rather than internal component state or implementation details.",
                concepts=[
                    LessonConcept(
                        title="Test User Interactions",
                        description="Simulate real user actions like clicking, typing, and navigating rather than calling component methods directly."
                    ),
                    LessonConcept(
                        title="Assert on Visible Outcomes",
                        description="Check what users would see or experience, not internal state changes or prop updates."
                    ),
                    LessonConcept(
                        title="Use Realistic Data",
                        description="Test with data that resembles what users would actually encounter in your application."
                    ),
                    LessonConcept(
                        title="Avoid Testing Implementation",
                        description="Don't test how something works internally; test that it works correctly for users."
                    )
                ],
                tip="Ask yourself: 'Would a real user care about this behavior?' If not, it might not need a test.",
                bad_example="""// ❌ Bad: Testing internal state
const component = shallow(<Counter />);
expect(component.state('count')).toBe(0);
component.instance().increment();
expect(component.state('count')).toBe(1);""",
                bad_explanation="This tests internal component state and methods that users never interact with directly.",
                good_example="""// ✅ Good: Testing user interactions and visible outcomes
render(<Counter />);
const counter = screen.getByText('Count: 0');
const incrementButton = screen.getByRole('button', { name: /increment/i });

await user.click(incrementButton);
expect(screen.getByText('Count: 1')).toBeInTheDocument();""",
                good_explanation="This tests the user's actual experience - what they see and what happens when they interact with the component.",
                exercise_description="Transform a component-focused test into a user-centric test.",
                exercise_instructions="Rewrite the test to focus on user interactions and visible outcomes instead of component internals.",
                initial_code="""import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import TodoList from './TodoList';

test('can add and complete todos', async () => {
  const user = userEvent.setup();
  const mockAddTodo = jest.fn();
  const mockToggleTodo = jest.fn();
  
  render(<TodoList onAddTodo={mockAddTodo} onToggleTodo={mockToggleTodo} />);
  
  // TODO: Focus on user interactions instead of mock function calls
  const input = screen.getByLabelText(/new todo/i);
  const addButton = screen.getByRole('button', { name: /add/i });
  
  await user.type(input, 'Learn React Testing');
  await user.click(addButton);
  
  expect(mockAddTodo).toHaveBeenCalledWith('Learn React Testing');
  
  // TODO: Test what the user sees, not just function calls
});"""
            )
        ),
        
        # Adding more lessons with similar structure...
        Lesson(
            id="query-hierarchy",
            title="Query Priority Hierarchy",
            description="Master the recommended query order for better accessibility",
            difficulty=DifficultyLevel.INTERMEDIATE,
            estimated_time=25,
            order=3,
            content=LessonContent(
                objective="Understand and apply the recommended query priority order to encourage better accessibility and more maintainable tests.",
                concepts=[
                    LessonConcept(
                        title="Queries Accessible to Everyone",
                        description="getByRole, getByLabelText, getByPlaceholderText, getByText should be your first choice."
                    ),
                    LessonConcept(
                        title="Semantic Queries",
                        description="getByDisplayValue, getByAltText are good for specific semantic elements."
                    ),
                    LessonConcept(
                        title="Test IDs - Last Resort",
                        description="getByTestId should only be used when no other query makes sense, as it doesn't reflect user behavior."
                    ),
                    LessonConcept(
                        title="Query Priority Benefits",
                        description="Following the hierarchy ensures your tests encourage accessible markup and are more resilient to changes."
                    )
                ],
                tip="The Testing Library docs provide a helpful query priority guide. Bookmark it for reference!",
                bad_example="""// ❌ Bad: Using test IDs first
const heading = screen.getByTestId('page-title');
const description = screen.getByTestId('page-description');
const button = screen.getByTestId('cta-button');""",
                bad_explanation="This overuses test IDs instead of leveraging semantic HTML and accessibility features.",
                good_example="""// ✅ Good: Following query hierarchy
const heading = screen.getByRole('heading', { name: /welcome/i });
const description = screen.getByText(/start your journey/i);
const button = screen.getByRole('button', { name: /get started/i });""",
                good_explanation="This uses semantic queries that match how assistive technologies and users find elements.",
                exercise_description="Refactor queries to follow the recommended priority hierarchy.",
                exercise_instructions="Replace the test ID queries with appropriate semantic queries following the recommended hierarchy.",
                initial_code="""import { render, screen } from '@testing-library/react';
import ProductCard from './ProductCard';

test('displays product information correctly', () => {
  const product = {
    name: 'Wireless Headphones',
    price: '$99.99',
    image: '/headphones.jpg',
    description: 'High-quality wireless headphones'
  };
  
  render(<ProductCard product={product} />);
  
  // TODO: Replace these with higher-priority queries
  expect(screen.getByTestId('product-name')).toHaveTextContent('Wireless Headphones');
  expect(screen.getByTestId('product-price')).toHaveTextContent('$99.99');
  expect(screen.getByTestId('product-image')).toHaveAttribute('src', '/headphones.jpg');
  expect(screen.getByTestId('add-to-cart')).toBeInTheDocument();
});"""
            )
        ),
        
        # Continue with remaining lessons...
        Lesson(
            id="simple-tests",
            title="Simple and Maintainable Tests",
            description="Keep tests straightforward and focused on essential functionality",
            difficulty=DifficultyLevel.BEGINNER,
            estimated_time=25,
            order=4,
            content=LessonContent(
                objective="Learn to write straightforward, focused tests that are easy to understand, maintain, and debug.",
                concepts=[
                    LessonConcept(
                        title="One Behavior Per Test",
                        description="Each test should verify a single piece of functionality to make failures easy to diagnose."
                    ),
                    LessonConcept(
                        title="Clear Test Names",
                        description="Test names should clearly describe what behavior is being tested from the user's perspective."
                    ),
                    LessonConcept(
                        title="Minimal Setup",
                        description="Only include the setup and data necessary for the specific test case."
                    ),
                    LessonConcept(
                        title="Avoid Complex Logic",
                        description="Tests should be straightforward to read and understand without complex conditionals or loops."
                    )
                ],
                tip="If your test is hard to understand or takes more than a few minutes to write, consider breaking it down into smaller, simpler tests.",
                bad_example="""// ❌ Bad: Complex test doing too much
test('form validation and submission', async () => {
  // ... complex multi-behavior test
});""",
                bad_explanation="This test is trying to verify multiple behaviors in one test, making it hard to understand what failed if it breaks.",
                good_example="""// ✅ Good: Simple, focused tests
test('shows validation error when name is empty', async () => {
  // ... single behavior test
});

test('submits form with valid data', async () => {
  // ... single behavior test
});""",
                good_explanation="Each test has a single, clear purpose. If one fails, you know exactly what behavior is broken.",
                exercise_description="Break down a complex test into simpler, focused tests.",
                exercise_instructions="Split the complex test into multiple simple tests, each focusing on one specific behavior.",
                initial_code="""import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import SearchForm from './SearchForm';

// TODO: Break this into multiple simple tests
test('search form functionality', async () => {
  const user = userEvent.setup();
  const mockSearch = jest.fn();
  render(<SearchForm onSearch={mockSearch} />);
  
  const input = screen.getByLabelText(/search/i);
  const button = screen.getByRole('button', { name: /search/i });
  
  // Test empty search
  await user.click(button);
  expect(screen.getByText(/please enter a search term/i)).toBeInTheDocument();
  
  // Test search with term
  await user.type(input, 'react testing');
  await user.click(button);
  expect(mockSearch).toHaveBeenCalledWith('react testing');
  
  // Test clear functionality
  const clearButton = screen.getByRole('button', { name: /clear/i });
  await user.click(clearButton);
  expect(input).toHaveValue('');
});"""
            )
        )
        
        # Add remaining lessons for brevity - the pattern continues...
    ]
    
    return lessons
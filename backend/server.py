from fastapi import <PERSON><PERSON><PERSON>, API<PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
from pathlib import Path
import os
import logging

# Import our models and services
from models import (
    Lesson, UserProgress, TestExecution, CourseStats,
    TestExecutionRequest, ProgressUpdateRequest,
    LessonListResponse, ProgressResponse
)
from database import db
from test_runner import test_runner

ROOT_DIR = Path(__file__).parent
load_dotenv(ROOT_DIR / '.env')

# Create the main app
app = FastAPI(title="RTL Course API", version="1.0.0")

# Create API router
api_router = APIRouter(prefix="/api")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@app.on_event("startup")
async def startup_db_client():
    """Initialize database connection and test environment"""
    try:
        await db.connect()
        logger.info("Database connected successfully")
        
        # Initialize test environment in background
        import asyncio
        asyncio.create_task(test_runner.setup_test_environment())
        
    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")

@app.on_event("shutdown")
async def shutdown_db_client():
    """Close database connection"""
    await db.disconnect()

# LESSON ENDPOINTS

@api_router.get("/lessons", response_model=LessonListResponse)
async def get_lessons():
    """Get all course lessons"""
    try:
        lessons = await db.get_all_lessons()
        return LessonListResponse(lessons=lessons)
    except Exception as e:
        logger.error(f"Error fetching lessons: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch lessons")

@api_router.get("/lessons/{lesson_id}", response_model=Lesson)
async def get_lesson(lesson_id: str):
    """Get a specific lesson by ID"""
    try:
        lesson = await db.get_lesson_by_id(lesson_id)
        if not lesson:
            raise HTTPException(status_code=404, detail="Lesson not found")
        return lesson
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching lesson {lesson_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch lesson")

# PROGRESS ENDPOINTS

@api_router.get("/progress", response_model=ProgressResponse)
async def get_progress(user_id: str = "anonymous"):
    """Get user progress and course statistics"""
    try:
        progress = await db.get_user_progress(user_id)
        stats = await db.get_course_stats(user_id)
        return ProgressResponse(progress=progress, stats=stats)
    except Exception as e:
        logger.error(f"Error fetching progress for user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch progress")

@api_router.post("/progress", response_model=UserProgress)
async def update_progress(request: ProgressUpdateRequest, user_id: str = "anonymous"):
    """Update user progress for a lesson"""
    try:
        progress = await db.update_user_progress(
            user_id=user_id,
            lesson_id=request.lesson_id,
            completed=request.completed
        )
        return progress
    except Exception as e:
        logger.error(f"Error updating progress: {e}")
        raise HTTPException(status_code=500, detail="Failed to update progress")

# TEST EXECUTION ENDPOINTS

@api_router.post("/execute-test", response_model=TestExecution)
async def execute_test(request: TestExecutionRequest):
    """Execute a React Testing Library test"""
    try:
        # Validate lesson exists
        lesson = await db.get_lesson_by_id(request.lesson_id)
        if not lesson:
            raise HTTPException(status_code=404, detail="Lesson not found")
        
        # Execute the test
        logger.info(f"Executing test for lesson: {request.lesson_id}")
        test_execution = await test_runner.execute_test(
            lesson_id=request.lesson_id,
            user_code=request.user_code
        )
        
        # Save execution results
        saved_execution = await db.save_test_execution(test_execution)
        
        logger.info(f"Test execution completed: {saved_execution.passed}")
        return saved_execution
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error executing test: {e}")
        raise HTTPException(status_code=500, detail=f"Test execution failed: {str(e)}")

@api_router.get("/test-execution/{execution_id}", response_model=TestExecution)
async def get_test_execution(execution_id: str):
    """Get a specific test execution result"""
    try:
        execution = await db.get_test_execution(execution_id)
        if not execution:
            raise HTTPException(status_code=404, detail="Test execution not found")
        return execution
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching test execution {execution_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch test execution")

# HEALTH CHECK

@api_router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "message": "RTL Course API is running",
        "test_environment": test_runner.setup_complete
    }

# Legacy endpoint for compatibility
@api_router.get("/")
async def root():
    return {"message": "RTL Course API - Ready to help you master React Testing Library!"}

# Include the router in the main app
app.include_router(api_router)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_credentials=True,
    allow_origins=["*"],
    allow_methods=["*"],
    allow_headers=["*"],
)

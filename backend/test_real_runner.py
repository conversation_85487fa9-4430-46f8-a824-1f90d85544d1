#!/usr/bin/env python3
"""
Test script to verify the real React Testing Library test runner works correctly.
This script tests the actual Jest/RTL integration without mocking.
"""

import asyncio
import sys
import os
import logging

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from test_runner import test_runner

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_environment_setup():
    """Test that the test environment can be set up correctly"""
    print("🔧 Testing test environment setup...")
    
    try:
        await test_runner.setup_test_environment()
        print("✅ Test environment setup successful")
        return True
    except Exception as e:
        print(f"❌ Test environment setup failed: {e}")
        return False

async def test_simple_rtl_test():
    """Test a simple React Testing Library test execution"""
    print("\n🧪 Testing simple RTL test execution...")
    
    # Simple test that should pass
    simple_test = """
test('renders a simple component', () => {
  render(<div>Hello World</div>);
  expect(screen.getByText('Hello World')).toBeInTheDocument();
});
"""
    
    try:
        result = await test_runner.execute_test("accessibility-queries", simple_test)
        
        print(f"Test passed: {result.passed}")
        print(f"Execution time: {result.execution_time:.2f}s")
        print(f"Number of tests: {len(result.tests)}")
        
        if result.tests:
            for test in result.tests:
                status = "✅" if test.passed else "❌"
                print(f"  {status} {test.name}: {test.message}")
        
        if result.error:
            print(f"Error: {result.error}")
        
        return result.passed
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        return False

async def test_accessibility_queries():
    """Test the accessibility queries lesson with proper RTL code"""
    print("\n🎯 Testing accessibility queries lesson...")
    
    # Good accessibility-focused test
    good_test = """
test('login form uses accessibility-first queries', () => {
  render(<LoginForm />);
  
  // Use getByLabelText for form inputs
  const emailInput = screen.getByLabelText(/email/i);
  const passwordInput = screen.getByLabelText(/password/i);
  
  // Use getByRole for buttons
  const submitButton = screen.getByRole('button', { name: /sign in/i });
  
  expect(emailInput).toBeInTheDocument();
  expect(passwordInput).toBeInTheDocument();
  expect(submitButton).toBeInTheDocument();
});
"""
    
    try:
        result = await test_runner.execute_test("accessibility-queries", good_test)
        
        print(f"Test passed: {result.passed}")
        print(f"Execution time: {result.execution_time:.2f}s")
        
        if result.tests:
            for test in result.tests:
                status = "✅" if test.passed else "❌"
                print(f"  {status} {test.name}")
                if not test.passed:
                    print(f"    Message: {test.message}")
        
        if result.error:
            print(f"Error: {result.error}")
        
        return result.passed
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        return False

async def test_bad_practices():
    """Test that bad testing practices are caught"""
    print("\n🚫 Testing bad testing practices detection...")
    
    # Bad test using implementation details
    bad_test = """
test('bad test using implementation details', () => {
  const { container } = render(<LoginForm />);
  
  // Bad: using querySelector and test IDs
  const emailInput = container.querySelector('[data-testid="email-input"]');
  const passwordInput = container.querySelector('[data-testid="password-input"]');
  const submitButton = container.querySelector('[data-testid="login-button"]');
  
  expect(emailInput).toBeInTheDocument();
  expect(passwordInput).toBeInTheDocument();
  expect(submitButton).toBeInTheDocument();
});
"""
    
    try:
        result = await test_runner.execute_test("accessibility-queries", bad_test)
        
        print(f"Test passed: {result.passed}")
        print(f"Execution time: {result.execution_time:.2f}s")
        
        # This test should technically pass but we want to detect bad practices
        if result.tests:
            for test in result.tests:
                status = "✅" if test.passed else "❌"
                print(f"  {status} {test.name}")
        
        return True  # We just want to see if it runs
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        return False

async def run_all_tests():
    """Run all test runner verification tests"""
    print("🚀 Starting React Testing Library Test Runner Verification\n")
    
    tests = [
        ("Environment Setup", test_environment_setup),
        ("Simple RTL Test", test_simple_rtl_test),
        ("Accessibility Queries", test_accessibility_queries),
        ("Bad Practices Test", test_bad_practices),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        try:
            success = await test_func()
            if success:
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print(f"\n{'='*50}")
    print("📊 Test Runner Verification Results")
    print('='*50)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 All test runner verification tests passed!")
        print("The React Testing Library test runner is working correctly.")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Check the errors above.")
    
    return failed == 0

if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)

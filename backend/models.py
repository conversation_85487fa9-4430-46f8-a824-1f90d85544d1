from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid
from enum import Enum

class DifficultyLevel(str, Enum):
    BEGINNER = "Beginner"
    INTERMEDIATE = "Intermediate"
    ADVANCED = "Advanced"

class LessonConcept(BaseModel):
    title: str
    description: str

class LessonContent(BaseModel):
    objective: str
    concepts: List[LessonConcept]
    tip: str
    bad_example: str
    bad_explanation: str
    good_example: str
    good_explanation: str
    exercise_description: str
    exercise_instructions: str
    initial_code: str

class Lesson(BaseModel):
    id: str
    title: str
    description: str
    difficulty: DifficultyLevel
    estimated_time: int
    content: LessonContent
    order: int

class TestResult(BaseModel):
    name: str
    passed: bool
    message: str
    details: Optional[str] = None

class TestCoverage(BaseModel):
    lines: float
    functions: float
    branches: float

class TestExecution(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    lesson_id: str
    user_code: str
    passed: bool
    tests: List[TestResult]
    coverage: TestCoverage
    execution_time: float
    error: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class UserProgress(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: str = "anonymous"  # For now, we'll use anonymous users
    lesson_id: str
    completed: bool = False
    completed_at: Optional[datetime] = None
    attempts: int = 0
    last_code: Optional[str] = None
    best_test_execution_id: Optional[str] = None

class CourseStats(BaseModel):
    total_lessons: int
    completed_lessons: int
    progress_percentage: float
    total_attempts: int
    successful_tests: int
    time_invested: int  # in minutes

# Request/Response Models
class TestExecutionRequest(BaseModel):
    lesson_id: str
    user_code: str

class ProgressUpdateRequest(BaseModel):
    lesson_id: str
    completed: bool = True

class LessonListResponse(BaseModel):
    lessons: List[Lesson]
    
class ProgressResponse(BaseModel):
    progress: List[UserProgress]
    stats: CourseStats
from motor.motor_asyncio import AsyncIOMotorClient
from typing import List, Optional
import os
from models import Lesson, UserProgress, TestExecution, CourseStats
import logging

logger = logging.getLogger(__name__)

class Database:
    def __init__(self):
        self.client = None
        self.db = None
    
    async def connect(self):
        try:
            mongo_url = os.environ.get('MONGO_URL')
            if not mongo_url:
                raise ValueError("MONGO_URL environment variable not set")
            
            self.client = AsyncIOMotorClient(mongo_url)
            self.db = self.client[os.environ.get('DB_NAME', 'rtl_course')]
            
            # Test connection
            await self.client.admin.command('ping')
            logger.info("Connected to MongoDB successfully")
            
            # Initialize with default data if needed
            await self._initialize_default_data()
            
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise
    
    async def disconnect(self):
        if self.client:
            self.client.close()
            logger.info("Disconnected from MongoDB")
    
    async def _initialize_default_data(self):
        """Initialize the database with default lesson data"""
        existing_lessons = await self.db.lessons.count_documents({})
        if existing_lessons == 0:
            logger.info("Initializing database with default lessons")
            await self._create_default_lessons()
    
    async def _create_default_lessons(self):
        """Create default lesson data"""
        from data.lesson_data import get_all_lessons
        lessons = get_all_lessons()
        
        for lesson in lessons:
            await self.db.lessons.insert_one(lesson.dict())
        
        logger.info(f"Created {len(lessons)} default lessons")
    
    # Lesson operations
    async def get_all_lessons(self) -> List[Lesson]:
        """Get all lessons ordered by sequence"""
        cursor = self.db.lessons.find({}).sort("order", 1)
        lessons_data = await cursor.to_list(length=None)
        return [Lesson(**lesson) for lesson in lessons_data]
    
    async def get_lesson_by_id(self, lesson_id: str) -> Optional[Lesson]:
        """Get a specific lesson by ID"""
        lesson_data = await self.db.lessons.find_one({"id": lesson_id})
        return Lesson(**lesson_data) if lesson_data else None
    
    # Progress operations
    async def get_user_progress(self, user_id: str = "anonymous") -> List[UserProgress]:
        """Get user progress for all lessons"""
        cursor = self.db.progress.find({"user_id": user_id})
        progress_data = await cursor.to_list(length=None)
        return [UserProgress(**progress) for progress in progress_data]
    
    async def update_user_progress(self, user_id: str, lesson_id: str, completed: bool = True) -> UserProgress:
        """Update or create user progress for a lesson"""
        progress_data = await self.db.progress.find_one({
            "user_id": user_id,
            "lesson_id": lesson_id
        })
        
        if progress_data:
            # Update existing progress
            update_data = {"completed": completed}
            if completed:
                from datetime import datetime
                update_data["completed_at"] = datetime.utcnow()
            
            await self.db.progress.update_one(
                {"user_id": user_id, "lesson_id": lesson_id},
                {"$set": update_data}
            )
            
            # Get updated progress
            updated_data = await self.db.progress.find_one({
                "user_id": user_id,
                "lesson_id": lesson_id
            })
            return UserProgress(**updated_data)
        else:
            # Create new progress
            from datetime import datetime
            progress = UserProgress(
                user_id=user_id,
                lesson_id=lesson_id,
                completed=completed,
                completed_at=datetime.utcnow() if completed else None
            )
            
            await self.db.progress.insert_one(progress.dict())
            return progress
    
    async def get_course_stats(self, user_id: str = "anonymous") -> CourseStats:
        """Get course statistics for a user"""
        # Get total lessons count
        total_lessons = await self.db.lessons.count_documents({})
        
        # Get user progress
        progress_cursor = self.db.progress.find({"user_id": user_id, "completed": True})
        completed_progress = await progress_cursor.to_list(length=None)
        completed_lessons = len(completed_progress)
        
        # Calculate progress percentage
        progress_percentage = (completed_lessons / total_lessons * 100) if total_lessons > 0 else 0
        
        # Get test execution stats
        total_attempts = await self.db.test_executions.count_documents({})
        successful_tests = await self.db.test_executions.count_documents({"passed": True})
        
        # Estimate time invested (25 minutes per completed lesson)
        time_invested = completed_lessons * 25
        
        return CourseStats(
            total_lessons=total_lessons,
            completed_lessons=completed_lessons,
            progress_percentage=round(progress_percentage, 1),
            total_attempts=total_attempts,
            successful_tests=successful_tests,
            time_invested=time_invested
        )
    
    # Test execution operations
    async def save_test_execution(self, test_execution: TestExecution) -> TestExecution:
        """Save a test execution result"""
        await self.db.test_executions.insert_one(test_execution.dict())
        
        # Update user progress attempts count
        await self.db.progress.update_one(
            {"user_id": "anonymous", "lesson_id": test_execution.lesson_id},
            {"$inc": {"attempts": 1}, "$set": {"last_code": test_execution.user_code}},
            upsert=True
        )
        
        # If test passed, update best test execution
        if test_execution.passed:
            await self.db.progress.update_one(
                {"user_id": "anonymous", "lesson_id": test_execution.lesson_id},
                {"$set": {"best_test_execution_id": test_execution.id}}
            )
        
        return test_execution
    
    async def get_test_execution(self, execution_id: str) -> Optional[TestExecution]:
        """Get a specific test execution by ID"""
        execution_data = await self.db.test_executions.find_one({"id": execution_id})
        return TestExecution(**execution_data) if execution_data else None

# Global database instance
db = Database()